import { Injectable, Logger } from '@nestjs/common';
import { PlatformSource } from '@domain/product/platform-source.enum';
import { ProductStatus } from '@domain/product/product-status.enum';
import { OrderStatus } from '@domain/order/order-status.enum';
import { ProductRepository } from '@infrastructure/database/repositories/product.repository';
import { OrderRepository } from '@infrastructure/database/repositories/order.repository';
import { OrderItemRepository } from '@infrastructure/database/repositories/order-item.repository';
import {
  EtsyApiService,
  EtsyListing,
  EtsyReceipt,
  EtsyTransaction,
} from '@infrastructure/external/etsy/etsy-api.service';
// import { PlatformSyncService } from './platform-sync.service'; // temporarily disabled
import {
  EtsyOrderSyncQueueService,
  EtsyOrderSyncQueueOptions,
} from './etsy-order-sync-queue.service';
import { ProductAttributeExtractor } from '@shared/utils/product-attribute-extractor.util';
import { PlatformAttributeExtractionService } from '@shared/services/platform-attribute-extraction.service';

export interface EtsySyncResult {
  platform: PlatformSource.ETSY;
  success: boolean;
  productsProcessed: number;
  ordersProcessed: number;
  errors: string[];
  duration: number;
  timestamp: Date;
}

@Injectable()
export class EtsySyncService {
  private readonly logger = new Logger(EtsySyncService.name);

  constructor(
    private readonly etsyApiService: EtsyApiService,
    private readonly productRepository: ProductRepository,
    private readonly orderRepository: OrderRepository,
    private readonly orderItemRepository: OrderItemRepository,
    // private readonly platformSyncService: PlatformSyncService, // temporarily disabled
    private readonly etsyOrderSyncQueueService: EtsyOrderSyncQueueService,
    private readonly platformAttributeExtractionService: PlatformAttributeExtractionService,
  ) {}

  /**
   * Sync all Etsy data (products and orders)
   */
  async syncAll(): Promise<EtsySyncResult> {
    const startTime = Date.now();
    const errors: string[] = [];
    let productsProcessed = 0;
    let ordersProcessed = 0;

    try {
      this.logger.log('Starting Etsy sync (products and orders)');

      // Sync products
      try {
        productsProcessed = await this.syncProducts();
      } catch (error) {
        this.logger.error('Failed to sync Etsy products:', error);
        errors.push(`Product sync failed: ${error.message}`);
      }

      // Sync orders
      try {
        ordersProcessed = await this.syncOrders();
      } catch (error) {
        this.logger.error('Failed to sync Etsy orders:', error);
        errors.push(`Order sync failed: ${error.message}`);
      }

      const result: EtsySyncResult = {
        platform: PlatformSource.ETSY,
        success: errors.length === 0,
        productsProcessed,
        ordersProcessed,
        errors,
        duration: Date.now() - startTime,
        timestamp: new Date(),
      };

      // Record sync result
      // await this.platformSyncService.recordSyncResult(result); // temporarily disabled

      return result;
    } catch (error) {
      this.logger.error('Etsy sync failed:', error);

      const result: EtsySyncResult = {
        platform: PlatformSource.ETSY,
        success: false,
        productsProcessed,
        ordersProcessed,
        errors: [...errors, error.message],
        duration: Date.now() - startTime,
        timestamp: new Date(),
      };

      // await this.platformSyncService.recordSyncResult(result); // temporarily disabled
      return result;
    }
  }

  /**
   * Sync Etsy products/listings
   */
  async syncProducts(): Promise<number> {
    this.logger.log('Starting Etsy product sync');
    let processed = 0;

    try {
      let offset = 0;
      const limit = 50;
      let hasMore = true;

      while (hasMore) {
        // Get listings from Etsy
        const listingsResponse = await this.etsyApiService.getShopListings({
          state: 'active',
          limit,
          offset,
          includes: ['Images', 'Inventory'],
        });

        const listings = listingsResponse.listings;
        hasMore = listings.length === limit;

        for (const listing of listings) {
          try {
            await this.processListing(listing);
            processed++;
          } catch (error) {
            this.logger.error(`Failed to process listing ${listing.listing_id}:`, error);
          }
        }

        offset += limit;

        // Add delay to respect rate limits
        if (hasMore) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }

      this.logger.log(`Processed ${processed} Etsy listings`);
      return processed;
    } catch (error) {
      this.logger.error('Failed to sync Etsy products:', error);
      throw error;
    }
  }

  /**
   * Sync Etsy orders/receipts
   */
  async syncOrders(): Promise<number> {
    this.logger.log('Starting Etsy order sync');
    let processed = 0;

    try {
      // Get orders from last 30 days
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      const minCreated = Math.floor(thirtyDaysAgo.getTime() / 1000);

      let offset = 0;
      const limit = 50;
      let hasMore = true;

      while (hasMore) {
        // Get receipts from Etsy
        const receiptsResponse = await this.etsyApiService.getShopReceipts({
          min_created: minCreated,
          limit,
          offset,
          sort_on: 'created',
          sort_order: 'down',
        });

        const receipts = receiptsResponse.receipts;
        hasMore = receipts.length === limit;

        for (const receipt of receipts) {
          try {
            await this.processReceipt(receipt);
            processed++;
          } catch (error) {
            this.logger.error(`Failed to process receipt ${receipt.receipt_id}:`, error);
          }
        }

        offset += limit;

        // Add delay to respect rate limits
        if (hasMore) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }

      this.logger.log(`Processed ${processed} Etsy receipts`);
      return processed;
    } catch (error) {
      this.logger.error('Failed to sync Etsy orders:', error);
      throw error;
    }
  }

  /**
   * Process listing from Etsy
   */
  private async processListing(listing: EtsyListing): Promise<void> {
    const sku = listing.sku || `ETSY-${listing.listing_id}`;

    // Check if product exists
    let product = await this.productRepository.findBySku(sku);

    if (!product) {
      // Create new product
      product = this.productRepository.create({
        sku,
        externalProductId: listing.listing_id.toString(),
        title: listing.title,
        status: this.mapEtsyListingState(listing.state),
        source: PlatformSource.ETSY,
        quantity: 0,
        amazonQuantity: 0,
        shopifyQuantity: 0,
        etsyQuantity: listing.quantity,
        price: listing.price.amount / listing.price.divisor,
        currency: listing.price.currency_code,
        externalCreatedAt: new Date(listing.creation_timestamp * 1000),
      });
    } else {
      // Update existing product
      product.updateEtsyInfo({
        quantity: listing.quantity,
      });

      if (listing.price) {
        product.price = listing.price.amount / listing.price.divisor;
        product.currency = listing.price.currency_code;
      }
    }

    await this.productRepository.save(product);
  }

  /**
   * Process receipt (order) from Etsy
   */
  private async processReceipt(receipt: EtsyReceipt): Promise<void> {
    // Check if order already exists
    const existingOrder = await this.orderRepository.findByExternalId(
      receipt.receipt_id.toString(),
      PlatformSource.ETSY,
    );

    if (existingOrder) {
      // Update existing order if needed
      this.updateExistingOrder(existingOrder, receipt);
      await this.orderRepository.save(existingOrder);
      return;
    }

    // Helper function to safely convert timestamp
    const safeTimestampToDate = (timestamp: any): Date => {
      if (!timestamp || isNaN(timestamp)) {
        this.logger.warn(`Invalid timestamp received: ${timestamp}, using current date`);
        return new Date();
      }
      return new Date(Number(timestamp) * 1000);
    };

    // Helper function to safely get price value
    const safePriceValue = (priceObj: any): number => {
      if (!priceObj || !priceObj.amount || !priceObj.divisor) {
        return 0;
      }
      return Number(priceObj.amount) / Number(priceObj.divisor);
    };

    // Create new order
    const order = this.orderRepository.create({
      externalOrderId: receipt.receipt_id?.toString() || '',
      orderNumber: receipt.receipt_id?.toString() || '',
      status: this.mapEtsyReceiptStatus(receipt),
      source: PlatformSource.ETSY,
      externalCreatedAt: safeTimestampToDate(receipt.creation_timestamp),
      customerName: receipt.name || '',
      customerEmail: receipt.payment_email || '',
      shippingAddressLine1: receipt.first_line || '',
      shippingAddressLine2: receipt.second_line || '',
      shippingCity: receipt.city || '',
      shippingState: receipt.state || '',
      shippingPostalCode: receipt.zip || '',
      shippingCountry: receipt.country_iso || '',
      subtotalPrice: safePriceValue(receipt.subtotal),
      shippingPrice: safePriceValue(receipt.total_shipping_cost),
      taxAmount: safePriceValue(receipt.total_tax_cost),
      discountAmount: safePriceValue(receipt.discount_amt),
      totalPrice: safePriceValue(receipt.grandtotal),
      currency: receipt.currency_code || 'USD',
      customerNote: receipt.message_from_buyer || '',
      isProcessed: false,
      rawData: {
        platform: 'etsy',
        receipt: receipt,
        syncedAt: new Date().toISOString(),
        apiVersion: 'v3',
      },
      metadata: {
        etsyData: {
          orderId: receipt.order_id,
          paymentMethod: receipt.payment_method,
          wasPaid: receipt.was_paid,
          needsGiftWrap: receipt.needs_gift_wrap,
          giftMessage: receipt.gift_message,
        },
      },
    });

    const savedOrder = await this.orderRepository.save(order);

    // Create order items from transactions
    for (const transaction of receipt.transactions) {
      await this.createOrderItem(savedOrder.id, transaction);
    }
  }

  /**
   * Create order item from Etsy transaction
   */
  private async createOrderItem(orderId: string, transaction: EtsyTransaction): Promise<void> {
    const sku = transaction.sku || `ETSY-${transaction.listing_id}`;

    // Helper function to safely get price value
    const safePriceValue = (priceObj: any): number => {
      if (!priceObj || !priceObj.amount || !priceObj.divisor) {
        return 0;
      }
      return Number(priceObj.amount) / Number(priceObj.divisor);
    };

    const unitPrice = safePriceValue(transaction.price);
    const quantity = Number(transaction.quantity) || 1;

    // Step 1: Extract attributes using basic extractor first
    const basicAttributes = ProductAttributeExtractor.extractAttributes({
      title: transaction.title,
      description: transaction.description,
      sku: transaction.sku,
      properties: transaction.variations?.map(v => ({
        name: v.formatted_name || '',
        value: v.formatted_value || '',
      })),
      variations: transaction.variations?.map(v => ({
        name: v.formatted_name || '',
        value: v.formatted_value || '',
      })),
      productData: transaction.product_data,
      metadata: {
        listingId: transaction.listing_id,
        transactionId: transaction.transaction_id,
      },
    });

    // Step 2: Create SKU extracted data (for now, use basic attributes as SKU extracted)
    const skuExtracted = {
      style: basicAttributes.style,
      color: basicAttributes.color,
      size: basicAttributes.size,
      design: basicAttributes.design,
      isCustom: basicAttributes.isCustom,
      isEngrave: basicAttributes.isEngrave,
      customization: basicAttributes.customization,
      engraving: basicAttributes.engraving,
      inlayType: undefined,
    };

    // Step 3: Use platform-specific extraction to fill in any remaining missing attributes
    const platformData = {
      platform: 'etsy',
      listingId: transaction.listing_id,
      transactionId: transaction.transaction_id,
      variations: transaction.variations,
      productData: transaction.product_data,
    };

    const finalAttributes = this.platformAttributeExtractionService.extractPlatformAttributes(
      PlatformSource.ETSY,
      skuExtracted,
      platformData,
      transaction.title,
      sku,
    );

    const orderItem = this.orderItemRepository.create({
      orderId,
      sku,
      title: transaction.title || '',
      style: finalAttributes.style,
      color: finalAttributes.color,
      size: finalAttributes.size,
      design: finalAttributes.design,
      quantity,
      unitPrice,
      totalPrice: unitPrice * quantity,
      currency: transaction.price?.currency_code || 'USD',
      externalProductId: transaction.listing_id?.toString() || '',
      isCustom: finalAttributes.isCustom,
      isEngrave: finalAttributes.isEngrave,
      customization: finalAttributes.customization,
      engraving: finalAttributes.engraving,
      rawData: {
        platform: 'etsy',
        transaction: transaction,
        syncedAt: new Date().toISOString(),
      },
      metadata: {
        etsyData: {
          transactionId: transaction.transaction_id,
          isDigital: transaction.is_digital,
          variations: transaction.variations,
          productData: transaction.product_data,
        },
        extractedAttributes: {
          basic: basicAttributes,
          skuExtracted: skuExtracted,
          final: finalAttributes,
        },
      },
    });

    await this.orderItemRepository.save(orderItem);
  }

  /**
   * Extract product attributes from Etsy transaction data
   */
  private extractProductAttributes(transaction: EtsyTransaction): {
    style?: string;
    color?: string;
    size?: number;
    design?: string;
    isCustom: boolean;
    isEngrave: boolean;
    customization?: string;
    engraving?: string;
  } {
    // Use the shared attribute extractor
    return ProductAttributeExtractor.extractAttributes({
      title: transaction.title,
      description: transaction.description,
      sku: transaction.sku,
      properties: transaction.variations?.map(v => ({
        name: v.formatted_name || '',
        value: v.formatted_value || '',
      })),
      variations: transaction.variations?.map(v => ({
        name: v.formatted_name || '',
        value: v.formatted_value || '',
      })),
      productData: transaction.product_data,
      metadata: {
        listingId: transaction.listing_id,
        transactionId: transaction.transaction_id,
      },
    });
  }

  /**
   * Legacy method - kept for backward compatibility
   * @deprecated Use ProductAttributeExtractor instead
   */
  private extractProductAttributesLegacy(transaction: EtsyTransaction): {
    style?: string;
    color?: string;
    size?: number;
    design?: string;
    isCustom: boolean;
    isEngrave: boolean;
    customization?: string;
    engraving?: string;
  } {
    const result = {
      style: undefined as string | undefined,
      color: undefined as string | undefined,
      size: undefined as number | undefined,
      design: undefined as string | undefined,
      isCustom: false,
      isEngrave: false,
      customization: undefined as string | undefined,
      engraving: undefined as string | undefined,
    };

    // 1. Extract from variations (Etsy's primary variation data)
    if (transaction.variations && Array.isArray(transaction.variations)) {
      for (const variation of transaction.variations) {
        const propertyName = variation.formatted_name?.toLowerCase() || '';
        const value = variation.formatted_value || '';

        if (propertyName.includes('color') || propertyName.includes('colour')) {
          result.color = value;
        } else if (propertyName.includes('size')) {
          // Extract size number from formatted value like "12 (6mm bandwidth)"
          const sizeMatch = value.match(/(\d+)/);
          if (sizeMatch) {
            result.size = parseInt(sizeMatch[1], 10);
          }
        } else if (propertyName.includes('style') || propertyName.includes('type')) {
          result.style = value;
        } else if (propertyName.includes('design') || propertyName.includes('pattern')) {
          result.design = value;
        }
      }
    }

    // 2. Extract from product_data (alternative variation format)
    if (transaction.product_data && Array.isArray(transaction.product_data)) {
      for (const productData of transaction.product_data) {
        const propertyName = productData.property_name?.toLowerCase() || '';
        const values = productData.values || [];
        const value = values[0] || '';

        if (
          propertyName.includes('color') ||
          propertyName.includes('colour') ||
          propertyName === 'primary color'
        ) {
          result.color = result.color || value;
        } else if (propertyName.includes('size') || propertyName === 'custom property') {
          // For custom property, check if it contains size info
          const sizeMatch = value.match(/(\d+)/);
          if (sizeMatch) {
            result.size = result.size || parseInt(sizeMatch[1], 10);
          }
        } else if (propertyName.includes('style') || propertyName.includes('type')) {
          result.style = result.style || value;
        } else if (propertyName.includes('design') || propertyName.includes('pattern')) {
          result.design = result.design || value;
        }
      }
    }

    // 3. Extract from SKU as fallback (format: Style-Width-Color-Size)
    if (transaction.sku && (!result.style || !result.color || !result.size)) {
      const skuParts = transaction.sku.split('-');

      if (skuParts.length >= 3) {
        // ComfortFit-6mm-Silver-12 format
        if (!result.style && skuParts[0]) {
          result.style = skuParts[0];
        }
        if (!result.color && skuParts[2]) {
          result.color = skuParts[2];
        }
        if (!result.size && skuParts[3]) {
          const sizeMatch = skuParts[3].match(/(\d+)/);
          if (sizeMatch) {
            result.size = parseInt(sizeMatch[1], 10);
          }
        }
      }
    }

    // 4. Check for customization and engraving
    const title = transaction.title?.toLowerCase() || '';
    const description = transaction.description?.toLowerCase() || '';

    // Check for engraving keywords
    if (
      title.includes('engrav') ||
      description.includes('engrav') ||
      title.includes('custom') ||
      description.includes('custom')
    ) {
      result.isEngrave = title.includes('engrav') || description.includes('engrav');
      result.isCustom = title.includes('custom') || description.includes('custom');
    }

    // Look for personalization in variations
    if (transaction.variations) {
      for (const variation of transaction.variations) {
        const propertyName = variation.formatted_name?.toLowerCase() || '';
        const value = variation.formatted_value || '';

        if (
          propertyName.includes('personalization') ||
          propertyName.includes('engraving') ||
          propertyName.includes('custom text') ||
          propertyName.includes('message')
        ) {
          if (
            value &&
            value.trim() &&
            value.toLowerCase() !== 'none' &&
            value.toLowerCase() !== 'no'
          ) {
            result.isEngrave = true;
            result.engraving = value;
          }
        } else if (propertyName.includes('customization') || propertyName.includes('custom')) {
          if (
            value &&
            value.trim() &&
            value.toLowerCase() !== 'none' &&
            value.toLowerCase() !== 'no'
          ) {
            result.isCustom = true;
            result.customization = value;
          }
        }
      }
    }

    return result;
  }

  /**
   * Update existing order with Etsy data
   */
  private updateExistingOrder(existingOrder: any, receipt: EtsyReceipt): void {
    const newStatus = this.mapEtsyReceiptStatus(receipt);

    if (existingOrder.status !== newStatus) {
      existingOrder.updateStatus(newStatus);
    }

    // Helper function to safely convert timestamp
    const safeTimestampToDate = (timestamp: any): Date => {
      if (!timestamp || isNaN(timestamp)) {
        return new Date();
      }
      return new Date(Number(timestamp) * 1000);
    };

    // Update metadata
    if (!existingOrder.metadata) {
      existingOrder.metadata = {};
    }
    existingOrder.metadata.etsyData = {
      ...existingOrder.metadata.etsyData,
      lastModified: safeTimestampToDate(receipt.last_modified_timestamp),
      wasPaid: receipt.was_paid,
    };
  }

  /**
   * Map Etsy listing state to our product status
   */
  private mapEtsyListingState(state: string): ProductStatus {
    switch (state.toLowerCase()) {
      case 'active':
        return ProductStatus.ACTIVE;
      case 'inactive':
        return ProductStatus.INACTIVE;
      case 'draft':
        return ProductStatus.DRAFT;
      case 'expired':
        return ProductStatus.ARCHIVED;
      default:
        return ProductStatus.INACTIVE;
    }
  }

  /**
   * Map Etsy receipt to our order status
   */
  private mapEtsyReceiptStatus(receipt: EtsyReceipt): OrderStatus {
    if (!receipt.was_paid) {
      return OrderStatus.PENDING;
    }

    // Check if any shipment exists
    if (receipt.shipments && receipt.shipments.length > 0) {
      return OrderStatus.SHIPPED;
    }

    return OrderStatus.CONFIRMED;
  }

  /**
   * Update inventory for a specific SKU
   */
  async updateInventory(sku: string, quantity: number): Promise<boolean> {
    try {
      this.logger.log(`Updating Etsy inventory for SKU ${sku} to quantity ${quantity}`);

      // Find the product to get listing ID
      const product = await this.productRepository.findBySku(sku);
      if (!product || !product.externalProductId) {
        this.logger.warn(`Product not found for SKU ${sku}`);
        return false;
      }

      const listingId = parseInt(product.externalProductId, 10);

      // Update listing quantity on Etsy
      const success = await this.etsyApiService.updateListingQuantity(listingId, quantity);

      if (success) {
        // Update local product record
        product.updateQuantityForPlatform(PlatformSource.ETSY, quantity);
        await this.productRepository.save(product);
      }

      return success;
    } catch (error) {
      this.logger.error(`Failed to update Etsy inventory for SKU ${sku}:`, error);
      return false;
    }
  }

  /**
   * Health check for Etsy API
   */
  async healthCheck(): Promise<boolean> {
    try {
      return await this.etsyApiService.healthCheck();
    } catch (error) {
      this.logger.error('Etsy health check failed:', error);
      return false;
    }
  }

  /**
   * Sync orders with date filters and duplicate prevention
   */
  async syncOrdersWithFilters(params: {
    startDate?: string;
    endDate?: string;
    limit: number;
    forceUpdate: boolean;
  }): Promise<{
    ordersProcessed: number;
    ordersCreated: number;
    ordersUpdated: number;
    ordersSkipped: number;
  }> {
    this.logger.log('Starting Etsy order sync with filters', params);

    let ordersProcessed = 0;
    let ordersCreated = 0;
    let ordersUpdated = 0;
    let ordersSkipped = 0;

    try {
      // Build Etsy API parameters
      const apiParams: any = {
        limit: Math.min(params.limit, 100), // Etsy max limit
      };

      // Add date filters if provided (Etsy uses Unix timestamps)
      if (params.startDate) {
        const startTimestamp = Math.floor(new Date(params.startDate).getTime() / 1000);
        apiParams.min_created = startTimestamp;
      }
      if (params.endDate) {
        const endTimestamp = Math.floor(new Date(params.endDate).getTime() / 1000);
        apiParams.max_created = endTimestamp;
      }

      this.logger.log('Fetching receipts from Etsy API', apiParams);

      // Fetch receipts from Etsy
      const receiptsResponse = await this.etsyApiService.getShopReceipts(apiParams);
      const receipts = receiptsResponse.receipts;

      this.logger.log(`Retrieved ${receipts.length} receipts from Etsy`);

      // Process each receipt
      for (const receipt of receipts) {
        try {
          ordersProcessed++;

          // Check if order already exists
          const existingOrder = await this.orderRepository.findByExternalId(
            receipt.receipt_id.toString(),
            PlatformSource.ETSY,
          );

          if (existingOrder) {
            if (params.forceUpdate) {
              // Update existing order
              const newStatus = this.mapEtsyReceiptStatus(receipt);
              if (existingOrder.status !== newStatus) {
                existingOrder.updateStatus(newStatus);
              }

              // Update metadata
              if (!existingOrder.metadata) {
                existingOrder.metadata = {};
              }
              existingOrder.metadata.etsyData = {
                ...existingOrder.metadata.etsyData,
                lastUpdated: new Date(),
                wasPaid: receipt.was_paid,
                paymentMethod: receipt.payment_method,
              };

              await this.orderRepository.save(existingOrder);
              ordersUpdated++;
              this.logger.debug(`Updated existing Etsy receipt: ${receipt.receipt_id}`);
            } else {
              // Skip existing order
              ordersSkipped++;
              this.logger.debug(`Skipped existing Etsy receipt: ${receipt.receipt_id}`);
            }
          } else {
            // Create new order
            await this.processReceipt(receipt);
            ordersCreated++;
            this.logger.debug(`Created new Etsy receipt: ${receipt.receipt_id}`);
          }
        } catch (error) {
          this.logger.error(`Failed to process Etsy receipt ${receipt.receipt_id}:`, error);
          // Continue processing other orders
        }
      }

      this.logger.log(
        `Etsy order sync completed: ${ordersProcessed} processed, ${ordersCreated} created, ${ordersUpdated} updated, ${ordersSkipped} skipped`,
      );

      return {
        ordersProcessed,
        ordersCreated,
        ordersUpdated,
        ordersSkipped,
      };
    } catch (error) {
      this.logger.error('Failed to sync Etsy orders:', error);
      throw error;
    }
  }

  /**
   * Start queue-based order sync for large datasets
   */
  async startQueueBasedOrderSync(options: EtsyOrderSyncQueueOptions): Promise<string> {
    this.logger.log('Starting queue-based Etsy order sync', options);

    try {
      const jobId = await this.etsyOrderSyncQueueService.startOrderSync(options);
      this.logger.log(`Queue-based Etsy order sync started with JobID: ${jobId}`);
      return jobId;
    } catch (error) {
      this.logger.error('Failed to start queue-based Etsy order sync:', error);
      throw error;
    }
  }

  /**
   * Get status of queue-based sync
   */
  async getQueueSyncStatus(jobId: string) {
    return this.etsyOrderSyncQueueService.getSyncStatus(jobId);
  }

  /**
   * Cancel queue-based sync
   */
  async cancelQueueSync(jobId: string): Promise<boolean> {
    return this.etsyOrderSyncQueueService.cancelSync(jobId);
  }

  /**
   * Get all active queue syncs
   */
  async getActiveQueueSyncs() {
    return this.etsyOrderSyncQueueService.getActiveSyncs();
  }

  /**
   * Determine whether to use queue-based sync based on parameters
   */
  shouldUseQueueSync(limit: number, dateRange?: { startDate?: string; endDate?: string }): boolean {
    // Use queue for large syncs (>100 orders) or when no date range is specified (full sync)
    if (limit > 100) {
      return true;
    }

    // Use queue if no date range specified (potentially large dataset)
    if (!dateRange?.startDate && !dateRange?.endDate) {
      return true;
    }

    // Use queue if date range is more than 30 days
    if (dateRange.startDate && dateRange.endDate) {
      const start = new Date(dateRange.startDate);
      const end = new Date(dateRange.endDate);
      const daysDiff = Math.abs(end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24);

      if (daysDiff > 30) {
        return true;
      }
    }

    return false;
  }

  /**
   * Generate new Etsy tokens using the existing generateEtsyToken method
   */
  async generateEtsyTokens(): Promise<{
    accessToken: string;
    refreshToken: string;
  }> {
    try {
      this.logger.log('Generating new Etsy tokens...');

      // Use the existing generateEtsyToken method from the API service
      const result = await this.etsyApiService.generateEtsyToken();

      this.logger.log('Successfully generated new Etsy tokens');

      return {
        accessToken: result.accessToken,
        refreshToken: result.newRefreshToken,
      };
    } catch (error) {
      this.logger.error('Failed to generate Etsy tokens:', error);
      throw error;
    }
  }
}
